import React, { useState, useRef } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  Edit3,
  Trash2,
  Copy,
  Check,
  X,
  MoreVertical
} from 'lucide-react';
import { Canvas } from '../types';

interface CanvasSidebarProps {
  canvases: Record<string, Canvas>;
  activeCanvasId: string;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onSwitchCanvas: (canvasId: string) => void;
  onCreateCanvas: () => void;
  onRenameCanvas: (canvasId: string, newName: string) => void;
  onDeleteCanvas: (canvasId: string) => void;
  onDuplicateCanvas: (canvasId: string) => void;
}

export const CanvasSidebar: React.FC<CanvasSidebarProps> = ({
  canvases,
  activeCanvasId,
  isCollapsed,
  onToggleCollapse,
  onSwitchCanvas,
  onCreateCanvas,
  onRenameCanvas,
  onDeleteCanvas,
  onDuplicateCanvas,
}) => {
  const [editingCanvasId, setEditingCanvasId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [showMenuForCanvas, setShowMenuForCanvas] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);
  const menuButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({});

  const canvasList = Object.values(canvases).sort((a, b) => a.createdAt - b.createdAt);

  const handleStartEdit = (canvas: Canvas) => {
    setEditingCanvasId(canvas.id);
    setEditingName(canvas.name);
    setShowMenuForCanvas(null);
    setMenuPosition(null);
  };

  const handleSaveEdit = () => {
    if (editingCanvasId && editingName.trim()) {
      onRenameCanvas(editingCanvasId, editingName.trim());
    }
    setEditingCanvasId(null);
    setEditingName('');
  };

  const handleCancelEdit = () => {
    setEditingCanvasId(null);
    setEditingName('');
  };

  const handleDelete = (canvasId: string) => {
    if (Object.keys(canvases).length > 1) {
      if (confirm('Are you sure you want to delete this canvas? This action cannot be undone.')) {
        onDeleteCanvas(canvasId);
      }
    } else {
      alert('Cannot delete the last canvas.');
    }
    setShowMenuForCanvas(null);
    setMenuPosition(null);
  };

  const handleDuplicate = (canvasId: string) => {
    onDuplicateCanvas(canvasId);
    setShowMenuForCanvas(null);
    setMenuPosition(null);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <>
      {/* Sidebar */}
      <div
        className={`fixed left-0 top-0 h-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-r border-gray-200/50 dark:border-gray-700/50 transition-all duration-300 z-40 ${
          isCollapsed ? 'w-12' : 'w-64'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200/50 dark:border-gray-700/50">
          {!isCollapsed && (
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Canvases
            </h2>
          )}
          <button
            onClick={onToggleCollapse}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300"
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </button>
        </div>

        {!isCollapsed && (
          <>
            {/* Add Canvas Button */}
            <div className="px-3 py-2 border-b border-gray-200/50 dark:border-gray-700/50">
              <button
                onClick={() => onCreateCanvas()}
                className="w-full flex items-center gap-2 px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                <span>New Canvas</span>
              </button>
            </div>

            {/* Canvas List */}
            <div className="flex-1 overflow-y-auto">
              {canvasList.map((canvas) => (
                <div
                  key={canvas.id}
                  className={`relative group border-b border-gray-100/50 dark:border-gray-800/50 ${
                    canvas.id === activeCanvasId
                      ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                  }`}
                >
                  <div
                    className="px-3 py-2 cursor-pointer"
                    onClick={() => onSwitchCanvas(canvas.id)}
                  >
                    {editingCanvasId === canvas.id ? (
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          value={editingName}
                          onChange={(e) => setEditingName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') handleCancelEdit();
                          }}
                          className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                          autoFocus
                        />
                        <button
                          onClick={handleSaveEdit}
                          className="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded"
                        >
                          <Check size={14} />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <h3
                              className={`text-sm font-medium truncate ${
                                canvas.id === activeCanvasId
                                  ? 'text-blue-900 dark:text-blue-100'
                                  : 'text-gray-900 dark:text-white'
                              }`}
                            >
                              {canvas.name}
                            </h3>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                              {formatDate(canvas.lastModified)}
                            </p>
                          </div>
                          <button
                            ref={(el) => {
                              menuButtonRefs.current[canvas.id] = el;
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              const newShowMenu = showMenuForCanvas === canvas.id ? null : canvas.id;
                              setShowMenuForCanvas(newShowMenu);

                              if (newShowMenu && menuButtonRefs.current[canvas.id]) {
                                const buttonRect = menuButtonRefs.current[canvas.id]!.getBoundingClientRect();
                                setMenuPosition({
                                  top: buttonRect.bottom + 4,
                                  left: buttonRect.left - 140 + buttonRect.width // Align right edge of menu with button
                                });
                              }
                            }}
                            className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-opacity"
                          >
                            <MoreVertical size={14} />
                          </button>
                        </div>


                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Overlay for mobile */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 bg-black/20 z-30 md:hidden"
          onClick={onToggleCollapse}
        />
      )}

      {/* Click outside to close menu */}
      {showMenuForCanvas && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowMenuForCanvas(null);
            setMenuPosition(null);
          }}
        />
      )}

      {/* Context Menu - positioned outside sidebar to avoid clipping */}
      {showMenuForCanvas && menuPosition && (
        <div
          className="fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[140px]"
          style={{
            top: `${menuPosition.top}px`,
            left: `${menuPosition.left}px`
          }}
        >
          <button
            onClick={() => handleStartEdit(canvasList.find(c => c.id === showMenuForCanvas)!)}
            className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 first:rounded-t-lg"
          >
            <Edit3 size={14} />
            Rename
          </button>
          <button
            onClick={() => handleDuplicate(showMenuForCanvas)}
            className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <Copy size={14} />
            Duplicate
          </button>
          {Object.keys(canvases).length > 1 && (
            <button
              onClick={() => handleDelete(showMenuForCanvas)}
              className="w-full flex items-center gap-2 px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 last:rounded-b-lg"
            >
              <Trash2 size={14} />
              Delete
            </button>
          )}
        </div>
      )}
    </>
  );
};
